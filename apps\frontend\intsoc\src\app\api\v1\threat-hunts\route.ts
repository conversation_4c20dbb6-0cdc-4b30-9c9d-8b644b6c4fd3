/**
 * Next.js API Route: /api/v1/threat-hunts
 * Proxies requests to the backend threat hunts endpoint
 */

import { NextRequest, NextResponse } from 'next/server';
import {
  backendApiProxy,
  BackendApiError,
} from '../../../../lib/backend-proxy';

export async function GET(request: NextRequest) {
  try {
    console.log('[API Route] GET /api/v1/threat-hunts - Proxying to backend...');

    // Check for batch request
    const { searchParams } = new URL(request.url);
    const batchEndpoints = searchParams.get('batch');

    if (batchEndpoints) {
      try {
        const endpoints = batchEndpoints.split(',');
        const results = await backendApiProxy.batchGet(endpoints);
        return NextResponse.json(
          { batch: results },
          {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'private, max-age=0, must-revalidate',
            },
          },
        );
      } catch (batchError) {
        console.error('[API Route] Batch request error:', batchError);
        return NextResponse.json(
          {
            error: 'Batch Request Failed',
            message: 'Failed to process batch request',
            timestamp: new Date().toISOString(),
          },
          { status: 500 },
        );
      }
    }

    // Forward the request to the backend with proper error handling
    let response;
    try {
      response = await backendApiProxy.forwardRequest('/api/v1/threat_hunts', {
        method: 'GET',
      });
    } catch (proxyError) {
      console.error('[API Route] Backend proxy error:', proxyError);
      
      if (proxyError instanceof BackendApiError) {
        return NextResponse.json(
          {
            error: 'Backend API Error',
            message: proxyError.message,
            timestamp: new Date().toISOString(),
          },
          { status: proxyError.status || 500 },
        );
      }

      return NextResponse.json(
        {
          error: 'Proxy Error',
          message: 'Failed to connect to backend service',
          timestamp: new Date().toISOString(),
        },
        { status: 503 },
      );
    }

    // Handle the response
    let responseData;
    try {
      responseData = await response.json();
    } catch (parseError) {
      console.error('[API Route] Failed to parse response:', parseError);
      return NextResponse.json(
        {
          error: 'Parse Error',
          message: 'Failed to parse backend response',
          timestamp: new Date().toISOString(),
        },
        { status: 502 },
      );
    }

    return NextResponse.json(responseData, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=0, must-revalidate',
      },
    });
  } catch (error) {
    console.error('[API Route] Unexpected error:', error);

    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: 'An unexpected error occurred',
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

/**
 * Health check endpoint
 */
export async function HEAD(_request: NextRequest) {
  try {
    // Perform a lightweight check to the backend
    const response = await backendApiProxy.forwardRequest('/api/v1/threat_hunts', {
      method: 'HEAD',
    });

    return new NextResponse(null, {
      status: response.status,
      headers: {
        'X-Health-Check': 'ok',
        'X-Backend-Status': response.status.toString(),
      },
    });
  } catch (error) {
    console.error('[API Route] Health check failed:', error);

    return new NextResponse(null, {
      status: 503,
      headers: {
        'X-Health-Check': 'failed',
        'X-Backend-Status': 'unavailable',
      },
    });
  }
}
