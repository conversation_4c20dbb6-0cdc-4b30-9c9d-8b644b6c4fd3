import { ThreatHunt, ThreatHuntsResponse } from '@telesoft/types';
import { apiClient } from '../api-client';

export interface ThreatHuntFilters {
  status?: ThreatHunt['status'];
  priority?: ThreatHunt['priority'];
  hunt_type?: ThreatHunt['hunt_type'];
  created_by?: string;
  assigned_to?: string;
}

export interface ThreatHuntStats {
  total: number;
  running: number;
  completed: number;
  failed: number;
  created: number;
  paused: number;
  byPriority: Record<ThreatHunt['priority'], number>;
  byType: Record<ThreatHunt['hunt_type'], number>;
  recentHunts: ThreatHunt[];
  activeHunts: ThreatHunt[];
}

class ThreatHuntsService {
  private static instance: ThreatHuntsService;

  static getInstance(): ThreatHuntsService {
    if (!ThreatHuntsService.instance) {
      ThreatHuntsService.instance = new ThreatHuntsService();
    }
    return ThreatHuntsService.instance;
  }

  /**
   * Fetch all threat hunts from the backend API
   */
  async getThreatHunts(): Promise<ThreatHunt[]> {
    try {
      const response = await apiClient.get<ThreatHuntsResponse>('/api/v1/threat-hunts', {
        skipCache: true, // Force skip cache for threat hunts data
      });
      return response.threat_hunts;
    } catch (error) {
      console.error('Failed to fetch threat hunts:', error);
      throw error;
    }
  }

  /**
   * Filter threat hunts based on criteria
   */
  filterThreatHunts(hunts: ThreatHunt[], filters: ThreatHuntFilters): ThreatHunt[] {
    return hunts.filter((hunt) => {
      if (filters.status && hunt.status !== filters.status) return false;
      if (filters.priority && hunt.priority !== filters.priority) return false;
      if (filters.hunt_type && hunt.hunt_type !== filters.hunt_type) return false;
      if (filters.created_by && hunt.created_by !== filters.created_by) return false;
      if (filters.assigned_to && hunt.assigned_to !== filters.assigned_to) return false;
      return true;
    });
  }

  /**
   * Get threat hunt statistics
   */
  getStats(hunts: ThreatHunt[]): ThreatHuntStats {
    const stats: ThreatHuntStats = {
      total: hunts.length,
      running: 0,
      completed: 0,
      failed: 0,
      created: 0,
      paused: 0,
      byPriority: {
        low: 0,
        medium: 0,
        high: 0,
        critical: 0,
      },
      byType: {
        automated: 0,
        manual: 0,
        scheduled: 0,
      },
      recentHunts: [],
      activeHunts: [],
    };

    hunts.forEach((hunt) => {
      // Count by status
      stats[hunt.status]++;

      // Count by priority
      stats.byPriority[hunt.priority]++;

      // Count by type
      stats.byType[hunt.hunt_type]++;
    });

    // Get recent hunts (last 10, sorted by created_at)
    stats.recentHunts = [...hunts]
      .sort((a, b) => (b.created_at || 0) - (a.created_at || 0))
      .slice(0, 10);

    // Get active hunts (running or created status)
    stats.activeHunts = hunts.filter(
      (hunt) => hunt.status === 'running' || hunt.status === 'created'
    );

    return stats;
  }

  /**
   * Get priority color for UI display
   */
  getPriorityColor(priority: ThreatHunt['priority']): string {
    switch (priority) {
      case 'critical':
        return 'text-cyber-critical-400';
      case 'high':
        return 'text-cyber-warning-400';
      case 'medium':
        return 'text-cyber-matrix-400';
      case 'low':
        return 'text-text-muted';
      default:
        return 'text-text-primary';
    }
  }

  /**
   * Get status color for UI display
   */
  getStatusColor(status: ThreatHunt['status']): string {
    switch (status) {
      case 'running':
        return 'text-cyber-matrix-400';
      case 'completed':
        return 'text-green-400';
      case 'failed':
        return 'text-cyber-critical-400';
      case 'paused':
        return 'text-cyber-warning-400';
      case 'created':
        return 'text-blue-400';
      default:
        return 'text-text-primary';
    }
  }

  /**
   * Format hunt duration
   */
  formatDuration(hunt: ThreatHunt): string {
    if (!hunt.started_at) return 'Not started';
    
    const endTime = hunt.completed_at || Date.now();
    const duration = endTime - hunt.started_at;
    
    const hours = Math.floor(duration / (1000 * 60 * 60));
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  }

  /**
   * Format relative time
   */
  formatRelativeTime(timestamp: number): string {
    const now = Date.now();
    const diff = now - timestamp;
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  }
}

export const threatHuntsService = ThreatHuntsService.getInstance();
