/**
 * Test file for threat hunts integration
 */

import { threatHuntsService } from '../lib/services/threat-hunts';
import { ThreatHunt } from '@telesoft/types';

// Mock the API client
jest.mock('../lib/api-client', () => ({
  apiClient: {
    get: jest.fn(),
  },
}));

import { apiClient } from '../lib/api-client';

describe('Threat Hunts Integration', () => {
  const mockThreatHunts: ThreatHunt[] = [
    {
      id: '1',
      name: 'Test Hunt 1',
      description: 'A test threat hunt',
      status: 'running',
      priority: 'high',
      hunt_type: 'automated',
      created_at: Date.now() - 3600000, // 1 hour ago
      updated_at: Date.now(),
      started_at: Date.now() - 1800000, // 30 minutes ago
      progress: 75,
      results_count: 5,
      query: 'SELECT * FROM logs WHERE suspicious = true',
      data_sources: ['logs', 'network'],
      tags: ['malware', 'network'],
      created_by: 'analyst1',
      assigned_to: 'analyst2',
    },
    {
      id: '2',
      name: 'Test Hunt 2',
      description: 'Another test threat hunt',
      status: 'completed',
      priority: 'medium',
      hunt_type: 'manual',
      created_at: Date.now() - 7200000, // 2 hours ago
      updated_at: Date.now() - 3600000, // 1 hour ago
      started_at: Date.now() - 7200000,
      completed_at: Date.now() - 3600000,
      progress: 100,
      results_count: 12,
      created_by: 'analyst2',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('threatHuntsService', () => {
    it('should fetch threat hunts from API', async () => {
      const mockResponse = { threat_hunts: mockThreatHunts };
      (apiClient.get as jest.Mock).mockResolvedValue(mockResponse);

      const result = await threatHuntsService.getThreatHunts();

      expect(apiClient.get).toHaveBeenCalledWith('/api/v1/threat-hunts', {
        skipCache: true,
      });
      expect(result).toEqual(mockThreatHunts);
    });

    it('should filter threat hunts correctly', () => {
      const filters = { status: 'running' as const };
      const result = threatHuntsService.filterThreatHunts(mockThreatHunts, filters);

      expect(result).toHaveLength(1);
      expect(result[0].status).toBe('running');
    });

    it('should calculate stats correctly', () => {
      const stats = threatHuntsService.getStats(mockThreatHunts);

      expect(stats.total).toBe(2);
      expect(stats.running).toBe(1);
      expect(stats.completed).toBe(1);
      expect(stats.byPriority.high).toBe(1);
      expect(stats.byPriority.medium).toBe(1);
      expect(stats.byType.automated).toBe(1);
      expect(stats.byType.manual).toBe(1);
      expect(stats.recentHunts).toHaveLength(2);
      expect(stats.activeHunts).toHaveLength(1);
    });

    it('should get correct priority colors', () => {
      expect(threatHuntsService.getPriorityColor('critical')).toBe('text-cyber-critical-400');
      expect(threatHuntsService.getPriorityColor('high')).toBe('text-cyber-warning-400');
      expect(threatHuntsService.getPriorityColor('medium')).toBe('text-cyber-matrix-400');
      expect(threatHuntsService.getPriorityColor('low')).toBe('text-text-muted');
    });

    it('should get correct status colors', () => {
      expect(threatHuntsService.getStatusColor('running')).toBe('text-cyber-matrix-400');
      expect(threatHuntsService.getStatusColor('completed')).toBe('text-green-400');
      expect(threatHuntsService.getStatusColor('failed')).toBe('text-cyber-critical-400');
      expect(threatHuntsService.getStatusColor('paused')).toBe('text-cyber-warning-400');
      expect(threatHuntsService.getStatusColor('created')).toBe('text-blue-400');
    });

    it('should format duration correctly', () => {
      const hunt = mockThreatHunts[0];
      const duration = threatHuntsService.formatDuration(hunt);
      
      // Should show some duration since it has started_at
      expect(duration).toMatch(/\d+m/);
    });

    it('should format relative time correctly', () => {
      const oneHourAgo = Date.now() - 3600000;
      const relativeTime = threatHuntsService.formatRelativeTime(oneHourAgo);
      
      expect(relativeTime).toBe('1h ago');
    });
  });

  describe('API Route Integration', () => {
    it('should handle empty threat hunts response', async () => {
      const mockResponse = { threat_hunts: [] };
      (apiClient.get as jest.Mock).mockResolvedValue(mockResponse);

      const result = await threatHuntsService.getThreatHunts();

      expect(result).toEqual([]);
    });

    it('should handle API errors gracefully', async () => {
      const mockError = new Error('API Error');
      (apiClient.get as jest.Mock).mockRejectedValue(mockError);

      await expect(threatHuntsService.getThreatHunts()).rejects.toThrow('API Error');
    });
  });
});
