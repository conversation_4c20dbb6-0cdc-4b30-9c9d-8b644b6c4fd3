import { useState, useEffect, useCallback, useMemo } from 'react';
import { ThreatHunt } from '@telesoft/types';
import { 
  threatHuntsService, 
  ThreatHuntFilters, 
  ThreatHuntStats 
} from '../services/threat-hunts';
import { ApiError } from '../api-client';

export interface UseThreatHuntsResult {
  threatHunts: ThreatHunt[];
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

/**
 * Hook for managing threat hunts data with loading and error states
 */
export function useThreatHunts(): UseThreatHuntsResult {
  const [threatHunts, setThreatHunts] = useState<ThreatHunt[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchThreatHunts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const hunts = await threatHuntsService.getThreatHunts();
      setThreatHunts(hunts);
    } catch (err) {
      console.error('Failed to fetch threat hunts:', err);
      if (err instanceof ApiError) {
        setError(`API Error: ${err.message}`);
      } else {
        setError('Failed to load threat hunts');
      }
    } finally {
      setLoading(false);
    }
  }, []);

  const refresh = useCallback(async () => {
    await fetchThreatHunts();
  }, [fetchThreatHunts]);

  useEffect(() => {
    fetchThreatHunts();
  }, [fetchThreatHunts]);

  return {
    threatHunts,
    loading,
    error,
    refresh,
  };
}

export interface UseThreatHuntFiltersResult {
  filteredHunts: ThreatHunt[];
  filters: ThreatHuntFilters;
  setFilters: (filters: ThreatHuntFilters) => void;
  resetFilters: () => void;
}

/**
 * Hook for filtering threat hunts
 */
export function useThreatHuntFilters(
  threatHunts: ThreatHunt[]
): UseThreatHuntFiltersResult {
  const [filters, setFilters] = useState<ThreatHuntFilters>({});

  const filteredHunts = useMemo(() => {
    return threatHuntsService.filterThreatHunts(threatHunts, filters);
  }, [threatHunts, filters]);

  const resetFilters = useCallback(() => {
    setFilters({});
  }, []);

  return {
    filteredHunts,
    filters,
    setFilters,
    resetFilters,
  };
}

/**
 * Hook for getting threat hunt statistics and distributions
 */
export function useThreatHuntStats(threatHunts: ThreatHunt[]): ThreatHuntStats {
  return useMemo(() => {
    return threatHuntsService.getStats(threatHunts);
  }, [threatHunts]);
}

/**
 * Hook for prefetching threat hunts data
 */
export function usePrefetchThreatHunts() {
  const prefetch = useCallback(async () => {
    try {
      await threatHuntsService.getThreatHunts();
    } catch (error) {
      console.error('Failed to prefetch threat hunts:', error);
    }
  }, []);

  return { prefetch };
}
